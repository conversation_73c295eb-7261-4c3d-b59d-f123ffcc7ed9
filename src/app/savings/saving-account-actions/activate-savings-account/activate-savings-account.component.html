<div class="container mat-elevation-z8">

  <mat-card>

    <form [formGroup]="activateSavingsAccountForm" (ngSubmit)="submit()">

      <mat-card-content>

        <mat-form-field fxFlex (click)="activatedOnDatePicker.open()">
          <mat-label>Activated On Date</mat-label>
          <input matInput [min]="minDate" [max]="maxDate" [matDatepicker]="activatedOnDatePicker" required formControlName="activatedOnDate">
          <mat-datepicker-toggle matSuffix [for]="activatedOnDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #activatedOnDatePicker></mat-datepicker>
          <mat-error *ngIf="activateSavingsAccountForm.controls.activatedOnDate.hasError('required')">
            Activated On Date is <strong>required</strong>
          </mat-error>
        </mat-form-field>

      </mat-card-content>

      <mat-card-actions fxLayout="row" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="5px">
        <button type="button" mat-raised-button [routerLink]="['../../transactions']">Cancel</button>
        <button mat-raised-button color="primary" [disabled]="!activateSavingsAccountForm.valid">Confirm</button>
      </mat-card-actions>

    </form>

  </mat-card>

</div>
