<div class="container">

  <h3>Loan Delinquency Tags</h3>

  <table mat-table [dataSource]="loanDelinquencyTags" *ngIf="loanDelinquencyTags.length>0">

    <ng-container matColumnDef="classification">
      <th mat-header-cell *matHeaderCellDef> Delinquency Classification </th>
      <td mat-cell *matCellDef="let item"> {{ item.delinquencyRange.classification }}
        <span *ngIf="item.delinquencyRange.maximumAgeDays">( {{ item.delinquencyRange.minimumAgeDays }} - {{ item.delinquencyRange.maximumAgeDays }} )</span>
        <span *ngIf="!item.delinquencyRange.maximumAgeDays">( {{ item.delinquencyRange.minimumAgeDays }} )</span>
      </td>
    </ng-container>

    <ng-container matColumnDef="addedOn">
      <th mat-header-cell *matHeaderCellDef> Added On </th>
      <td mat-cell *matCellDef="let item">
        <span *ngIf="item.addedOnDate">
          {{ item.addedOnDate  | dateFormat }}
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="liftedOn">
      <th mat-header-cell *matHeaderCellDef> Lifted On </th>
      <td mat-cell *matCellDef="let item">
        <span *ngIf="item.liftedOnDate">
          {{ item.liftedOnDate  | dateFormat }}
        </span>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="loanDelinquencyTagsColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: loanDelinquencyTagsColumns;"></tr>
  </table>
</div>
