<form [formGroup]="fixedDepositProductAccountingForm">

  <div fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

    <mat-radio-group fxFlex="98%" fxLayout="row" fxLayoutGap="5%" fxLayout.lt-md="column" formControlName="accountingRule">
      <mat-radio-button *ngFor="let accountingRule of accountingRuleData; let i =  index" [value]="i+1">
        {{ accountingRule }}
      </mat-radio-button>
    </mat-radio-group>

    <mat-divider fxFlex="98%"></mat-divider>

    <div *ngIf="fixedDepositProductAccountingForm.value.accountingRule === 2" fxFlexFill fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

      <h4 fxFlex="98%" class="mat-h4">Assets</h4>

      <mat-form-field fxFlex="48%">
        <mat-label>Saving reference</mat-label>
        <mat-select formControlName="savingsReferenceAccountId" required>
          <mat-option *ngFor="let assetAccount of assetAccountData" [value]="assetAccount.id">
            ({{ assetAccount.glCode }}) {{ assetAccount.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Saving reference is <strong>required</strong>
        </mat-error>
      </mat-form-field>

      <mat-divider fxFlex="98%"></mat-divider>

      <h4 fxFlex="98%" class="mat-h4">Liabilities</h4>

      <mat-form-field fxFlex="48%">
        <mat-label>Saving control</mat-label>
        <mat-select formControlName="savingsControlAccountId" required>
          <mat-option *ngFor="let liabilityAccount of liabilityAccountData" [value]="liabilityAccount.id">
            ({{ liabilityAccount.glCode }}) {{ liabilityAccount.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Saving control is <strong>required</strong>
        </mat-error>
      </mat-form-field>

      <mat-form-field fxFlex="48%">
        <mat-label>Savings transfers in suspense</mat-label>
        <mat-select formControlName="transfersInSuspenseAccountId" required>
          <mat-option *ngFor="let liabilityAccount of liabilityAccountData" [value]="liabilityAccount.id">
            ({{ liabilityAccount.glCode }}) {{ liabilityAccount.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Savings transfers in suspense is <strong>required</strong>
        </mat-error>
      </mat-form-field>

      <mat-divider fxFlex="98%"></mat-divider>

      <h4 fxFlex="98%" class="mat-h4">Expenses</h4>

      <mat-form-field fxFlex="48%">
        <mat-label>Interest on savings</mat-label>
        <mat-select formControlName="interestOnSavingsAccountId" required>
          <mat-option *ngFor="let expenseAccount of expenseAccountData" [value]="expenseAccount.id">
            ({{ expenseAccount.glCode }}) {{ expenseAccount.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Interest on savings is <strong>required</strong>
        </mat-error>
      </mat-form-field>

      <mat-divider fxFlex="98%"></mat-divider>

      <h4 fxFlex="98%" class="mat-h4">Income</h4>

      <mat-form-field fxFlex="48%">
        <mat-label>Income from fees</mat-label>
        <mat-select formControlName="incomeFromFeeAccountId" required>
          <mat-option *ngFor="let incomeAccount of incomeAccountData" [value]="incomeAccount.id">
            ({{ incomeAccount.glCode }}) {{ incomeAccount.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Income from fees Repayments is <strong>required</strong>
        </mat-error>
      </mat-form-field>

      <mat-form-field fxFlex="48%">
        <mat-label>Income from penalties</mat-label>
        <mat-select formControlName="incomeFromPenaltyAccountId" required>
          <mat-option *ngFor="let incomeAccount of incomeAccountData" [value]="incomeAccount.id">
            ({{ incomeAccount.glCode }}) {{ incomeAccount.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Income from penalties is <strong>required</strong>
        </mat-error>
      </mat-form-field>

      <mat-divider fxFlex="98%"></mat-divider>

      <h3 fxFlex="23%" class="mat-h3">Advanced Accounting Rules</h3>

      <mat-checkbox fxFlex="73%" formControlName="advancedAccountingRules"></mat-checkbox>

      <div *ngIf="fixedDepositProductAccountingForm.value.advancedAccountingRules" fxFlexFill fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

        <h4 fxFlex="33%" class="mat-h4">Configure Fund Sources for Payment Channels</h4>

        <div fxFlex="63%">
          <button type="button" mat-raised-button color="primary" (click)="add('PaymentFundSource', paymentChannelToFundSourceMappings)">
            <fa-icon icon="plus" class="m-r-10"></fa-icon>
            Add
          </button>
        </div>

        <table fxFlex="98%" class="mat-elevation-z1" mat-table [dataSource]="paymentChannelToFundSourceMappings.value" *ngIf="paymentChannelToFundSourceMappings.value.length !== 0">

          <ng-container matColumnDef="paymentTypeId">
            <th mat-header-cell *matHeaderCellDef> Payment Type </th>
            <td mat-cell *matCellDef="let paymentFundSource">
              {{ paymentFundSource.paymentTypeId | find:paymentTypeData:'id':'name' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="fundSourceAccountId">
            <th mat-header-cell *matHeaderCellDef> Fund Source </th>
            <td mat-cell *matCellDef="let paymentFundSource">
              {{ paymentFundSource.fundSourceAccountId | find:assetAccountData:'id':'name' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let paymentFundSource; let i = index">
              <button mat-icon-button color="primary" (click)="edit('PaymentFundSource', paymentChannelToFundSourceMappings, i)">
                <fa-icon icon="edit"></fa-icon>
              </button>
              <button mat-icon-button color="warn" (click)="delete(paymentChannelToFundSourceMappings, i)">
                <fa-icon icon="trash"></fa-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="paymentFundSourceDisplayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: paymentFundSourceDisplayedColumns;"></tr>

        </table>

        <h4 fxFlex="33%" class="mat-h4">Map Fees to Specific Income Accounts</h4>

        <div fxFlex="63%">
          <button type="button" mat-raised-button color="primary" (click)="add('FeesIncome', feeToIncomeAccountMappings)">
            <fa-icon icon="plus" class="m-r-10"></fa-icon>
            Add
          </button>
        </div>

        <table fxFlex="98%" class="mat-elevation-z1" mat-table [dataSource]="feeToIncomeAccountMappings.value" *ngIf="feeToIncomeAccountMappings.value.length !== 0">

          <ng-container matColumnDef="chargeId">
            <th mat-header-cell *matHeaderCellDef> Fees </th>
            <td mat-cell *matCellDef="let feesIncome">
              {{ feesIncome.chargeId | find:chargeData:'id':'name' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="incomeAccountId">
            <th mat-header-cell *matHeaderCellDef> Income Account </th>
            <td mat-cell *matCellDef="let feesIncome">
              {{ feesIncome.incomeAccountId | find:incomeAccountData:'id':'name' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let feesIncome; let i = index">
              <button mat-icon-button color="primary" (click)="edit('FeesIncome', feeToIncomeAccountMappings, i)">
                <fa-icon icon="edit"></fa-icon>
              </button>
              <button mat-icon-button color="warn" (click)="delete(feeToIncomeAccountMappings, i)">
                <fa-icon icon="trash"></fa-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="feesPenaltyIncomeDisplayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: feesPenaltyIncomeDisplayedColumns;"></tr>

        </table>

        <h4 fxFlex="33%" class="mat-h4">Map Penalties to Specific Income Accounts</h4>

        <div fxFlex="63%">
          <button type="button" mat-raised-button color="primary" (click)="add('PenaltyIncome', penaltyToIncomeAccountMappings)">
            <fa-icon icon="plus" class="m-r-10"></fa-icon>
            Add
          </button>
        </div>


        <table fxFlex="98%" class="mat-elevation-z1" mat-table [dataSource]="penaltyToIncomeAccountMappings.value" *ngIf="penaltyToIncomeAccountMappings.value.length !== 0">

          <ng-container matColumnDef="chargeId">
            <th mat-header-cell *matHeaderCellDef> Penalty </th>
            <td mat-cell *matCellDef="let penaltyIncome">
              {{ penaltyIncome.chargeId | find:penaltyData:'id':'name' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="incomeAccountId">
            <th mat-header-cell *matHeaderCellDef> Income Account </th>
            <td mat-cell *matCellDef="let penaltyIncome">
              {{ penaltyIncome.incomeAccountId | find:incomeAccountData:'id':'name' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let penaltyIncome; let i = index">
              <button mat-icon-button color="primary" (click)="edit('PenaltyIncome', penaltyToIncomeAccountMappings, i)">
                <fa-icon icon="edit"></fa-icon>
              </button>
              <button mat-icon-button color="warn" (click)="delete(penaltyToIncomeAccountMappings, i)">
                <fa-icon icon="trash"></fa-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="feesPenaltyIncomeDisplayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: feesPenaltyIncomeDisplayedColumns;"></tr>

        </table>

      </div>

    </div>

  </div>

  <div fxLayout="row" class="margin-t" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="2%">
    <button mat-raised-button matStepperPrevious>
      <fa-icon icon="arrow-left" class="m-r-10"></fa-icon>
      Previous
    </button>
    <button mat-raised-button matStepperNext [disabled]="!fixedDepositProductFormValid">
      Next
      <fa-icon icon="arrow-right" class="m-l-10"></fa-icon>
    </button>
  </div>

</form>
