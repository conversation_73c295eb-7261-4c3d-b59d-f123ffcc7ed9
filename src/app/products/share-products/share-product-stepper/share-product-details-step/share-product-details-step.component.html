<form [formGroup]="shareProductDetailsForm">

  <div fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

    <mat-form-field fxFlex="48%">
      <mat-label>Product Name</mat-label>
      <input matInput formControlName="name" matTooltip="A unique identifier for the share product" required>
      <mat-error>
        Product Name is <strong>required</strong>
      </mat-error>
    </mat-form-field>

    <mat-form-field fxFlex="48%">
      <mat-label>Short Name</mat-label>
      <input matInput formControlName="shortName" matTooltip="A unique identifier for the share product" maxlength="4" required>
      <mat-error>
        Short Name is <strong>required</strong>
      </mat-error>
    </mat-form-field>

    <mat-form-field fxFlex="98%">
      <mat-label>Description</mat-label>
      <textarea matInput formControlName="description" matTooltip="Provides additional information regarding the purpose and characteristics of the share product" required></textarea>
    </mat-form-field>

  </div>

  <div fxLayout="row" class="margin-t" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="2%">
    <button mat-raised-button matStepperPrevious disabled>
      <fa-icon icon="arrow-left" class="m-r-10"></fa-icon>
      Previous
    </button>
    <button mat-raised-button matStepperNext>
      Next
      <fa-icon icon="arrow-right" class="m-l-10"></fa-icon>
    </button>
  </div>

</form>
