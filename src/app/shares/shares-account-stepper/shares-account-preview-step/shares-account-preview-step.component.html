<div fxLayout="row wrap" fxLayout.lt-md="column">

  <h3 class="mat-h3" fxFlexFill>Details</h3>

  <mat-divider fxFlexFill></mat-divider>

  <div fxFlexFill>
    <span fxFlex="40%">Product</span>
    <span fxFlex="60%">{{ sharesAccount.productId | find:sharesAccountTemplate.productOptions:'id':'name' }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Submitted On</span>
    <span fxFlex="60%">{{ sharesAccount.submittedDate  | dateFormat }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">External Id</span>
    <span fxFlex="60%" *ngIf="sharesAccount.externalId">
      <mifosx-external-identifier externalId="{{sharesAccount.externalId}}"></mifosx-external-identifier>
    </span>
    <span fxFlex="60%" *ngIf="!sharesAccount.externalId">
      Unassigned
    </span>
  </div>

  <h3 class="mat-h3" fxFlexFill>Terms</h3>

  <mat-divider fxFlexFill></mat-divider>

  <div fxFlexFill>
    <span fxFlex="40%">Currency</span>
    <span fxFlex="60%">{{ sharesAccountTermsForm.get('currencyCode').value | find:[sharesAccountProductTemplate.currency]:'code':'displayLabel' }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Decimal Places</span>
    <span fxFlex="60%">{{ sharesAccountTermsForm.get('decimal').value }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Total Number of Shares</span>
    <span fxFlex="60%">{{ sharesAccount.requestedShares }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Today's Price</span>
    <span fxFlex="60%">{{ sharesAccountTermsForm.get('unitPrice').value }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Currency in multiples of</span>
    <span fxFlex="60%">{{ sharesAccountTermsForm.get('currencyMultiple').value }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Default Savings Account</span>
    <span fxFlex="60%">{{ sharesAccount.savingsAccountId | find:sharesAccountProductTemplate.clientSavingsAccounts:'id':'accountNo' }}</span>
  </div>

  <div fxFlexFill *ngIf="sharesAccount.minimumActivePeriod">
    <span fxFlex="40%">Minimum Active Period</span>
    <span fxFlex="60%">{{sharesAccount.minimumActivePeriod}}&nbsp;{{sharesAccount.minimumActivePeriodFrequencyType | find:sharesAccountProductTemplate.minimumActivePeriodFrequencyTypeOptions:'id':'value'}}</span>
  </div>

  <div fxFlexFill *ngIf="sharesAccount.lockinPeriodFrequency">
    <span fxFlex="40%">Lock-in Period</span>
    <span fxFlex="60%">{{sharesAccount.lockinPeriodFrequency}}&nbsp;{{sharesAccount.lockinPeriodFrequencyType | find:sharesAccountProductTemplate.lockinPeriodFrequencyTypeOptions:'id':'value'}}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Application Date</span>
    <span fxFlex="60%">{{ sharesAccount.applicationDate  | dateFormat }}</span>
  </div>

  <div fxFlexFill>
    <span fxFlex="40%">Allow dividends for inactive clients</span>
    <span fxFlex="60%">{{ sharesAccount.allowDividendCalculationForInactiveClients }}</span>
  </div>

  <div fxFlexFill *ngIf="sharesAccount.charges.length" fxLayout="row wrap" fxLayout.lt-md="column">

    <h3 class="mat-h3" fxFlexFill>Charges</h3>

    <mat-divider fxFlexFill></mat-divider>

    <table fxFlexFill class="mat-elevation-z1" mat-table [dataSource]="sharesAccount.charges">

      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef> Name </th>
        <td mat-cell *matCellDef="let charge">
          {{ charge.name + ', ' + charge.currency.displaySymbol }}
        </td>
      </ng-container>

      <ng-container matColumnDef="chargeCalculationType">
        <th mat-header-cell *matHeaderCellDef> Type </th>
        <td mat-cell *matCellDef="let charge">
          {{ charge.chargeCalculationType.value }}
        </td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef> Amount </th>
        <td mat-cell *matCellDef="let charge">
          {{ charge.amount || charge.amountOrPercentage }}
        </td>
      </ng-container>

      <ng-container matColumnDef="chargeTimeType">
        <th mat-header-cell *matHeaderCellDef> Collected On </th>
        <td mat-cell *matCellDef="let charge">
          {{ charge.chargeTimeType.value }}
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="chargesDisplayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: chargesDisplayedColumns;"></tr>

    </table>

  </div>

</div>

<div fxLayout="row" class="margin-t" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="2%">
  <button mat-raised-button matStepperPrevious>
    <fa-icon icon="arrow-left" class="m-r-10"></fa-icon>
    Previous
  </button>
  <button mat-raised-button [routerLink]="['../']">
    Cancel
  </button>
  <button mat-raised-button color="primary" (click)="submit.emit()">
    Submit
  </button>
</div>
