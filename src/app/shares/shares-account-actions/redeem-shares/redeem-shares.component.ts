/** Angular Imports */
import { Component, OnInit, Input } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

/** Custom Services */
import { SharesService } from 'app/shares/shares.service';
import { SettingsService } from 'app/settings/settings.service';
import { Dates } from 'app/core/utils/dates';

/**
 * Redeem Shares Component
 */
@Component({
  selector: 'mifosx-redeem-shares',
  templateUrl: './redeem-shares.component.html',
  styleUrls: ['./redeem-shares.component.scss']
})
export class RedeemSharesComponent implements OnInit {

  /** Shares account data. */
  sharesAccountData: any;

  complet: boolean = true;

  paymentTypeOptions: {
    id: number,
    name: string,
    description: string,
    isCashPayment: boolean,
    position: number
  }[];
  paymentDetailsRequired :boolean = false;
  /** Flag to enable payment details fields. */
  addPaymentDetailsFlag: Boolean = false;

  /** Minimum date allowed. */
  minDate = new Date(2000, 0, 1);
  /** Maximum date allowed. */
  maxDate = new Date();
  /** Redeem Share Account form. */
  redeemSharesForm: UntypedFormGroup;
  /** Shares Account Id */
  accountId: any;
  
  isSubmitted = false;
  /**
   * @param {FormBuilder} formBuilder Form Builder
   * @param {SharesService} sharesService Shares Service
   * @param {Dates} dateUtils Date Utils
   * @param {ActivatedRoute} route Activated Route
   * @param {Router} router Router
   * @param {SettingsService} settingsService Settings Service.
   */
  constructor(private formBuilder: UntypedFormBuilder,
              private sharesService: SharesService,
              private dateUtils: Dates,
              private route: ActivatedRoute,
              private router: Router,
              private settingsService: SettingsService) {
    this.accountId = this.route.parent.snapshot.params['shareAccountId'];
    this.route.data.subscribe((data: { shareAccountActionData: any }) => {
      this.sharesAccountData = data.shareAccountActionData;
      this.paymentTypeOptions = data.shareAccountActionData.paymentTypeOptions;
    });
  }

  /**
   * Creates the apply shares form.
   * Fetching data from service as action buttons malfunction
   * in clients view upon using a common resolver.
   */
  ngOnInit() {
    this.maxDate = this.settingsService.businessDate;
    this.createRedeemSharesAccountForm();
    this.redeemSharesForm.get('unitPrice').patchValue(this.sharesAccountData.currentMarketPrice || '');
  }

  /**
   * Creates the apply shares form.
   */
  createRedeemSharesAccountForm() {
    this.redeemSharesForm = this.formBuilder.group({
      'requestedDate': ['', Validators.required],
      'requestedShares': ['', Validators.required],
      'paymentTypeId': [''],
      'unitPrice': [{value: '', disabled: true}]
    });
    this.redeemSharesForm.controls.paymentTypeId.valueChanges.subscribe(paymentType => {
      var type = this.paymentTypeOptions.filter((option) => {
        return option.id == paymentType;
      })[0];
      if (!type.isCashPayment) {
        this.addPaymentDetailsFlag = false;
        this.paymentDetailsRequired = true;
      } else {
        this.addPaymentDetailsFlag = true;
        this.paymentDetailsRequired = false;
      }
      this.addPaymentDetails();
    });
  }

  /**
   * Method to add payment detail fields to the UI.
   */
    addPaymentDetails() {
      this.addPaymentDetailsFlag = !this.addPaymentDetailsFlag;
      if (this.addPaymentDetailsFlag) {
        this.redeemSharesForm.addControl('accountNumber', new UntypedFormControl(''));
        this.redeemSharesForm.addControl('checkNumber', new UntypedFormControl(''));
        this.redeemSharesForm.addControl('routingCode', new UntypedFormControl(''));
        if (this.paymentDetailsRequired) {
          this.redeemSharesForm.addControl('receiptNumber', new UntypedFormControl('', [Validators.required]));
        } else {
          this.redeemSharesForm.addControl('receiptNumber', new UntypedFormControl(''));
        }
        this.redeemSharesForm.addControl('bankNumber', new UntypedFormControl(''));
      } else {
        this.redeemSharesForm.removeControl('accountNumber');
        this.redeemSharesForm.removeControl('checkNumber');
        this.redeemSharesForm.removeControl('routingCode');
        this.redeemSharesForm.removeControl('receiptNumber');
        this.redeemSharesForm.removeControl('bankNumber');
      }
    }

  /**
   * Submits the form and applies additional shares to the share account,
   * if successful redirects to the share account.
   */
  submit() {
    const redeemSharesFormData = this.redeemSharesForm.value;
    const locale = this.settingsService.language.code;
    const dateFormat = this.settingsService.dateFormat;
    const prevRequestedDate: Date = this.redeemSharesForm.value.requestedDate;
    if (redeemSharesFormData.requestedDate instanceof Date) {
      redeemSharesFormData.requestedDate = this.dateUtils.formatDate(prevRequestedDate, dateFormat);
    }
    const data = {
      ...redeemSharesFormData,
      unitPrice: this.redeemSharesForm.get('unitPrice').value,
      dateFormat,
      locale
    };
    if (!this.isSubmitted) {
      this.isSubmitted = true; // Disable submit after first click
    }
    this.sharesService.executeSharesAccountCommand(this.accountId, 'redeemshares', data).subscribe(() => {
      this.router.navigate(['../../'], { relativeTo: this.route });
    }, () => {
      this.complet = true;
    }, () => {
      this.complet = true;
    });
  }

}
