/** Angular Imports */
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl } from '@angular/forms';
import { ClientsService } from 'app/clients/clients.service';
import { Dates } from 'app/core/utils/dates';

/** RxJS Imports */
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, switchMap } from 'rxjs/operators';

/** Custom Services */
import { SettingsService } from 'app/settings/settings.service';

/**
 * Create Client Component
 */
@Component({
  selector: 'mifosx-client-general-step',
  templateUrl: './client-general-step.component.html',
  styleUrls: ['./client-general-step.component.scss']
})
export class ClientGeneralStepComponent implements OnInit, OnDestroy {

  @Output() legalFormChangeEvent = new EventEmitter<{ legalForm: number }>();

  /** Minimum date allowed. */
  minDate = new Date(2000, 0, 1);
  /** Maximum date allowed. */
  maxDate = new Date();

  /** Client Template */
  @Input() clientTemplate: any;
  /** Create Client Form */
  createClientForm: UntypedFormGroup;

  /** Office Options */
  officeOptions: any;
  /** Staff Options */
  staffOptions: any;
  /** Legal Form Options */
  legalFormOptions: any;
  /** Client Type Options */
  clientTypeOptions: any;
  /** Client Classification Options */
  clientClassificationTypeOptions: any;
  /** Business Line Options */
  businessLineOptions: any;
  /** Constitution Options */
  constitutionOptions: any;
  /** Gender Options */
  genderOptions: any;
  /** Saving Product Options */
  savingProductOptions: any;
  /** Marital Status Options */
  maritalStatusOptions: any;
  /** Profession Options */
  professionOptions: any;
  /** Qualification Options */
  qualificationOptions: any;
  /** Referral Type Options */
  referralTypeOptions: any[];

  showClientSearch = false;
  showStaffSearch = false;
  clients: any;
  staffMembers: any;
  selectedClientName: string;
  selectedStaffName: string;

  // debouncing search
  private clientSearchSubject = new Subject<string>();
  private staffSearchSubject = new Subject<string>();

  private subscriptions: Subscription[] = [];

  // Display function for autocomplete
  displayFn = (client: any): string => {

    if (typeof client === 'string') {
      return client;
    }

    if (client) {
      if (client.accountNo) {
        return client.accountNo;
      } else if (client.accountNumber) {
        return client.accountNumber;
      }
    }

    return '';
  }

  // Display function for staff autocomplete
  displayStaffFn = (staff: any): string => {
    if (typeof staff === 'string') {
      return staff;
    }

    if (staff) {
      if (staff.entityType === 'STAFF' && staff.entityName) {
        if (staff.entityId !== undefined) {
          this._lastSelectedStaffId = staff.entityId;
        }
        return staff.entityName;
      }

      // For direct staff objects
      if (staff.firstname && staff.lastname) {
        // Store the id in the staff object for later use
        if (staff.id !== undefined) {
          this._lastSelectedStaffId = staff.id;
        }
        // Use "lastname, firstname" format to be consistent
        return `${staff.lastname}, ${staff.firstname}`;
      }

      // Fallback to displayName
      if (staff.displayName) {
        if (staff.id !== undefined) {
          this._lastSelectedStaffId = staff.id;
        }
        return staff.displayName;
      }
    }

    return '';
  }

  // Store the last selected staff ID
  private _lastSelectedStaffId: any = null;

  /**
   * @param {FormBuilder} formBuilder Form Builder
   * @param {Dates} dateUtils Date Utils
   * @param {SettingsService} settingsService Setting service
   * @param {ClientsService} clientService Client service
   */
  constructor(private formBuilder: UntypedFormBuilder,
              private dateUtils: Dates,
              private settingsService: SettingsService,
              private clientService: ClientsService) {
    this.setClientForm();
  }

  ngOnInit() {
    this.maxDate = this.settingsService.businessDate;
    this.setOptions();
    this.buildDependencies();
    this.setupSearchDebouncing();

    // Initialize staffMembers with staffOptions
    this.staffMembers = this.staffOptions ? [...this.staffOptions] : [];
  }

  /**
   * Setup debouncing for search inputs
   */
  setupSearchDebouncing() {
    // Client search debouncing
    const clientSearchSubscription = this.clientSearchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(text => text.length > 2),
      switchMap(text => this.clientService.searchByText(text, 0, 10))
    ).subscribe((result: any) => {
      this.clients = result.content;
    });

    // Staff search debouncing
    const staffSearchSubscription = this.staffSearchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(text => text.length > 2),
      switchMap(text => this.clientService.searchStaffByText(text))
    ).subscribe((result: any) => {
      if (result && Array.isArray(result)) {
        this.staffMembers = result.filter((item: any) => item.entityType === 'STAFF');
      } else {
        this.staffMembers = [];
      }
    });

    this.subscriptions.push(clientSearchSubscription, staffSearchSubscription);
  }

  /**
   * Cleanup subscriptions on component destroy
   */
  ngOnDestroy() {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());

    this.clientSearchSubject.complete();
    this.staffSearchSubject.complete();
  }

  /**
   * Creates the client form.
   */
  setClientForm() {
    this.createClientForm = this.formBuilder.group({
      'officeId': ['', Validators.required],
      'staffId': [''],
      'legalFormId': ['', Validators.required],
      'isStaff': [false],
      'active': [false],
      'addSavings': [false],
      'accountNo': [''],
      'externalId': [''],
      'genderId': ['', Validators.required],
      'mobileNo': [''],
      'emailAddress': ['', Validators.email],
      'dateOfBirth': [''],
      'clientTypeId': [''],
      'clientClassificationId': [''],
      'referralTypeId': [''],
      'referralAccountNo': [''],
      'referredByClientId': [''],
      'staffSearchQuery': [''],
      'referredByStaffId': [''],
      'referralSourceDescription': [''],
      'submittedOnDate': [this.settingsService.businessDate, Validators.required],
      // Additional fields
      'maritalStatusId': [''],
      'professionId': [''],
      'qualificationId': ['']
    });
  }

  /**
   * Sets select dropdown options.
   */
  setOptions() {
    this.officeOptions = this.clientTemplate.officeOptions;
    this.staffOptions = this.clientTemplate.staffOptions;
    this.legalFormOptions = this.clientTemplate.clientLegalFormOptions;
    this.clientTypeOptions = this.clientTemplate.clientTypeOptions;
    this.clientClassificationTypeOptions = this.clientTemplate.clientClassificationOptions;
    this.businessLineOptions = this.clientTemplate.clientNonPersonMainBusinessLineOptions;
    this.constitutionOptions = this.clientTemplate.clientNonPersonConstitutionOptions;
    this.genderOptions = this.clientTemplate.genderOptions;
    this.savingProductOptions = this.clientTemplate.savingProductOptions;
    // Always use backend codevalue keys and naming
    this.maritalStatusOptions = this.clientTemplate.maritalStatusOptions || [];
    this.professionOptions = this.clientTemplate.professionOptions || [];
    this.qualificationOptions = this.clientTemplate.qualificationOptions || [];
    this.referralTypeOptions = this.clientTemplate.referralTypeOptions || [];
  }

  /**
   * Adds controls conditionally.
   */
  buildDependencies() {
    this.createClientForm.get('legalFormId').valueChanges.subscribe((legalFormId: number) => {
      this.legalFormChangeEvent.emit({ legalForm: legalFormId });
      if (legalFormId === 1) {
        this.createClientForm.removeControl('fullname');
        this.createClientForm.removeControl('clientNonPersonDetails');
        // Updated form controls to support Unicode characters
        this.createClientForm.addControl('firstname', new UntypedFormControl('', [Validators.required]));
        this.createClientForm.addControl('middlename', new UntypedFormControl(''));
        this.createClientForm.addControl('lastname', new UntypedFormControl('', [Validators.required]));
      } else {
        this.createClientForm.removeControl('firstname');
        this.createClientForm.removeControl('middlename');
        this.createClientForm.removeControl('lastname');
        this.createClientForm.addControl('fullname', new UntypedFormControl('', [Validators.required]));
        this.createClientForm.addControl('clientNonPersonDetails', this.formBuilder.group({
          'constitutionId': ['', Validators.required],
          'incorpValidityTillDate': [''],
          'incorpNumber': [''],
          'mainBusinessLineId': [''],
          'remarks': ['']
        }));
      }
    });
    this.createClientForm.get('legalFormId').patchValue(1);
    this.createClientForm.get('active').valueChanges.subscribe((active: boolean) => {
      if (active) {
        this.createClientForm.addControl('activationDate', new UntypedFormControl('', Validators.required));
      } else {
        this.createClientForm.removeControl('activationDate');
      }
    });
    // this.createClientForm.get('addSavings').valueChanges.subscribe((active: boolean) => {
    //   if (active) {
    //     this.createClientForm.addControl('savingsProductId', new UntypedFormControl('', Validators.required));
    //   } else {
    //     this.createClientForm.removeControl('savingsProductId');
    //   }
    // });
    this.createClientForm.get('officeId').valueChanges.subscribe((officeId: number) => {
      this.clientService.getClientWithOfficeTemplate(officeId).subscribe((clientTemplate: any) => {
        this.staffOptions = clientTemplate.staffOptions;
      });
    });

    this.createClientForm.get('referralTypeId').valueChanges.subscribe((referralTypeId: any) => {
      // Find the selected referral type
      const selectedReferralType = this.referralTypeOptions.find(option => option.id === referralTypeId);
      const referralTypeName = selectedReferralType ? selectedReferralType.name.toLowerCase() : '';

      // Check if it's a client referral
      this.showClientSearch = referralTypeName === 'client';

      // Check if it's a staff referral
      this.showStaffSearch = referralTypeName === 'staff';

      if (!this.showClientSearch) {
        this.createClientForm.patchValue({
          'referralAccountNo': '',
          'referredByClientId': ''
        });
      }

      if (!this.showStaffSearch) {
        this.createClientForm.patchValue({
          'staffSearchQuery': '',
          'referredByStaffId': ''
        });
      }

      if (!this.showClientSearch && !this.showStaffSearch) {
        this.createClientForm.patchValue({
          'referralSourceDescription': ''
        });
      } else {
        this.createClientForm.patchValue({
          'referralSourceDescription': ''
        });
      }
    });

    const clientSearchFormSubscription = this.createClientForm.get('referralAccountNo').valueChanges.subscribe((value: any) => {

      if (this.showClientSearch && typeof value === 'string' && value !== '') {
        this.clientSearchSubject.next(value);

        if (this.createClientForm.get('referredByClientId').value) {
          this.createClientForm.patchValue({
            'referredByClientId': ''
          });
          this.selectedClientName = '';
        }
      }
    });

    this.subscriptions.push(clientSearchFormSubscription);

    // Handle staff search
    const staffSearchFormSubscription = this.createClientForm.get('staffSearchQuery').valueChanges.subscribe((value: any) => {

      if (this.showStaffSearch && typeof value === 'string' && value !== '') {
        this.staffSearchSubject.next(value);

        if (this.createClientForm.get('referredByStaffId').value) {
          this.createClientForm.patchValue({
            'referredByStaffId': ''
          });
          this.selectedStaffName = '';
        }
      }
    });

    // Add to subscriptions for cleanup
    this.subscriptions.push(staffSearchFormSubscription);
  }

  /**
   * Handles client selection from autocomplete
   * @param {any} clientValue Selected client value (can be object or string)
   */
  clientSelected(clientValue: any) {

    if (typeof clientValue === 'string') {
      const selectedClient = this.clients.find((c: any) =>
        (c.accountNumber && c.accountNumber === clientValue) ||
        (c.accountNo && c.accountNo === clientValue)
      );

      if (selectedClient) {

        this.selectedClientName = selectedClient.displayName;

        const accountNumber = selectedClient.accountNo || selectedClient.accountNumber;

        this.createClientForm.patchValue({
          'referralAccountNo': accountNumber,
          'referredByClientId': selectedClient.id
        });
      }
    } else if (clientValue && clientValue.id) {

      this.selectedClientName = clientValue.displayName;

      const accountNumber = clientValue.accountNo || clientValue.accountNumber;

      this.createClientForm.patchValue({
        'referralAccountNo': accountNumber,
        'referredByClientId': clientValue.id
      });

      setTimeout(() => {
        this.createClientForm.get('referralAccountNo').updateValueAndValidity();
      }, 0);
    }

    setTimeout(() => {
      const accountNo = this.createClientForm.get('referralAccountNo').value;
    }, 100);
  }

  /**
   * Handles staff selection from autocomplete
   * @param {any} staffValue Selected staff value
   */
  staffSelected(staffValue: any) {

    if (typeof staffValue === 'string') {
      let selectedStaff = this.staffMembers && this.staffMembers.find((s: any) => {
        const displayName = this.displayStaffFn(s);
        return displayName === staffValue;
      });

      if (!selectedStaff) {
        selectedStaff = this.staffOptions.find((s: any) => this.displayStaffFn(s) === staffValue);
      }

      if (selectedStaff) {

        const displayName = this.displayStaffFn(selectedStaff);

        this.selectedStaffName = displayName;

        const staffId = selectedStaff.entityId !== undefined ? selectedStaff.entityId : selectedStaff.id;
        this._lastSelectedStaffId = staffId;

        this.createClientForm.patchValue({
          'staffSearchQuery': displayName,
          'referredByStaffId': staffId
        });

      }
    } else if (staffValue) {

      const displayName = this.displayStaffFn(staffValue);

      this.selectedStaffName = displayName;

      const staffId = staffValue.entityId !== undefined ? staffValue.entityId : staffValue.id;
      this._lastSelectedStaffId = staffId;

      this.createClientForm.patchValue({
        'staffSearchQuery': displayName,
        'referredByStaffId': staffId
      });

      setTimeout(() => {
        this.createClientForm.get('staffSearchQuery').updateValueAndValidity();
      }, 0);
    }
  }



  /**
   * Client General Details
   */
  get clientGeneralDetails() {
    const generalDetails = this.createClientForm.value;
    const dateFormat = this.settingsService.dateFormat;
    const locale = this.settingsService.language.code;

    // using copy to avoid modifying the original form
    const clientData = {...generalDetails};

    if (typeof clientData.referralAccountNo === 'object' && clientData.referralAccountNo !== null) {
      clientData.referralAccountNo = clientData.referralAccountNo.accountNo ||
                                    clientData.referralAccountNo.accountNumber;
    }

    if (clientData.staffSearchQuery) {
      delete clientData.staffSearchQuery;
    }

    if (clientData.referredByStaffId) {

      if (typeof clientData.referredByStaffId === 'string') {
        clientData.referredByStaffId = parseInt(clientData.referredByStaffId, 10);
      }

      // If we have a staff ID but no staff name, fetch the staff name for display in preview
      if (!this.selectedStaffName) {
        this.clientService.getStaffById(clientData.referredByStaffId)
          .subscribe(
            (staffData: any) => {
              if (staffData) {
                if (staffData.firstname && staffData.lastname) {
                  this.selectedStaffName = `${staffData.firstname} ${staffData.lastname}`;
                } else {
                  this.selectedStaffName = staffData.displayName || `Staff ID: ${clientData.referredByStaffId}`;
                }
              }
            },
            (error) => {
              this.selectedStaffName = `Staff ID: ${clientData.referredByStaffId}`;
            }
          );
      }
    } else {
      const referralTypeId = clientData.referralTypeId;
      if (referralTypeId) {
        const selectedReferralType = this.referralTypeOptions.find((option: any) => option.id === referralTypeId);
        const referralTypeName = selectedReferralType ? selectedReferralType.name.toLowerCase() : '';
        if (referralTypeName === 'staff') {
          if (this._lastSelectedStaffId !== null) {
            clientData.referredByStaffId = this._lastSelectedStaffId;
          } else {
            clientData.referredByStaffId = null;
          }
        }
      }
    }

    // Remove empty fields to ensure they don't override existing values
    for (const key in clientData) {
      if ((clientData[key] === '' && key !== 'referredByStaffId') || key === 'addSavings') {
        delete clientData[key];
      } else if (key === 'referredByStaffId' && clientData[key] === '') {
        clientData[key] = null;
      }
    }

    // Format dates
    if (clientData.submittedOnDate instanceof Date) {
      clientData.submittedOnDate = this.dateUtils.formatDate(clientData.submittedOnDate, dateFormat);
    }
    if (clientData.activationDate instanceof Date) {
      clientData.activationDate = this.dateUtils.formatDate(clientData.activationDate, dateFormat);
    }
    if (clientData.dateOfBirth instanceof Date) {
      clientData.dateOfBirth = this.dateUtils.formatDate(clientData.dateOfBirth, dateFormat);
    }

    if (clientData.clientNonPersonDetails && clientData.clientNonPersonDetails.incorpValidityTillDate) {
      clientData.clientNonPersonDetails = {
        ...clientData.clientNonPersonDetails,
        incorpValidityTillDate: this.dateUtils.formatDate(clientData.clientNonPersonDetails.incorpValidityTillDate, dateFormat),
        dateFormat,
        locale
      };
    }
    return clientData;
  }
}
