/** Angular Imports */
import { Component, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

/** rxjs Imports */
import { merge, forkJoin } from 'rxjs';
import { skip } from 'rxjs/operators';

/** Custom Services */
import { HomeService } from '../../home.service';

/** Charting Imports */
import Chart from 'chart.js';
import { Dates } from 'app/core/utils/dates';

/**
 * Savings By Product Doughnut Chart Component.
 */
@Component({
  selector: 'savings-by-product',
  templateUrl: './savings-by-product.component.html',
  styleUrls: ['./savings-by-product.component.scss']
})
export class SavingsByProductComponent implements OnInit {

  /** Static Form control for office Id */
  officeId = new UntypedFormControl();
  /** Static Form control for time scale */
  timescale = new UntypedFormControl();
  /** Office Data */
  officeData: any;
  /** Chart.js chart */
  chart: any;
  /** Substitute for resolver */
  hideOutput = true;

  /**
   * Fetches offices data from `resolve`
   * @param {HomeService} homeService Home Service
   * @param {ActivatedRoute} route Activated Route
   * @param {Dates} dateUtils Date Utils
   */
  constructor(private homeService: HomeService,
              private route: ActivatedRoute,
              private dateUtils: Dates) {
    this.route.data.subscribe( (data: { offices: any }) => {
      this.officeData = data.offices;
    });
  }

  ngOnInit() {
    this.getChartData();
    this.initializeControls();
  }

  /**
   * Initialize the form controls for better UX.
   */
  initializeControls() {
    this.officeId.patchValue(1);
    this.timescale.patchValue('Total');
  }

  /**
   * Subscribes to value changes of officeID and timescale controls,
   * Fetches data accordingly and sets charts based on fetched data.
   */
  getChartData() {
    merge(this.officeId.valueChanges, this.timescale.valueChanges).pipe(skip(1))
      .subscribe(() => {
        const officeId = this.officeId.value;
        const savingsByProduct = this.homeService.getSavingsByProduct(officeId);
        forkJoin([savingsByProduct]).subscribe((data: any[]) => {
          const savingsByProductCounts = this.getCounts(data[0]);

          this.setChart(savingsByProductCounts[0], savingsByProductCounts[1]);
          this.hideOutput = false;
        });

    });
  }

  /**
   * Get bar heights for savings/loans trends.
   * @param {any[]} response API response array.
   * @param {any[]} labels Abscissa Labels.
   * @param {string} timescale User's timescale choice.
   * @param {string} type 'saving' or 'loan'.
   */
  getCounts(response: any[]) {
    let savings: number[]  = [];
    let productNames: string[] = [];

    response.forEach((entry : any) => {
      savings.push(entry.balance);
      productNames.push(entry.product_name);
    });

    return [productNames, savings];
  }


  /**
   * Creates an instance of Chart.js multi-bar chart.
   * Refer: https://www.chartjs.org/docs/latest/charts/bar.html for configuration details.
   * @param {any[]} labels Abscissa Labels.
   * @param {number[]} savingsByProduct Savings Ordinate.

   */
  setChart(labels: any[], savingsByProduct: any[]) {
    if (!this.chart) {
      this.chart = new Chart('savings-by-product', {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Total Savings',
              data: savingsByProduct,
              fill: false,
              backgroundColor: [
                '#EC407A',
                '#FFCA28',
                '#29B6F6',
                '#26A69A',
                '#7E57C2'
              ],
            }
          ]
        },
        options: {
          layout: {
            padding: {
              top: 5,
              left: 10,
              right: 10
            }
          },
          plugins: {
            legend: false,
            labels: labels
          }
        }
      });
    } else {
      this.chart.data.labels = labels;
      this.chart.data.datasets[0].data = savingsByProduct;
      this.chart.update();
    }
  }

}
