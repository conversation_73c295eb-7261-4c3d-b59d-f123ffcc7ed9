<div class="container">

  <mat-card>

    <mat-card-content>

      <div fxLayout="row wrap" fxLayout.lt-md="column">

        <h3 class="mat-h3" fxFlexFill>Transaction Details</h3>

        <mat-divider [inset]="true"></mat-divider>

        <div fxFlexFill>
          <span fxFlex="40%">Transaction Amount:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.currency.displaySymbol }} {{ viewAccountTransferData.transferAmount | formatNumber }} ({{ viewAccountTransferData.currency.code }})</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Transaction Date:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.transferDate  | dateFormat }}</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Description:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.transferDescription }}</span>
        </div>

        <h3 class="mat-h3" fxFlexFill>Transferred From</h3>

        <mat-divider [inset]="true"></mat-divider>

        <div fxFlexFill>
          <span fxFlex="40%">Office:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.fromOffice.name }}</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Client:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.fromClient.displayName }}</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Account Type:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.fromAccountType.value }}</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Account No:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.fromAccount.accountNo }}</span>
        </div>

        <h3 class="mat-h3" fxFlexFill>Transferred To</h3>

        <mat-divider [inset]="true"></mat-divider>

        <div fxFlexFill>
          <span fxFlex="40%">Office:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.toOffice.name }}</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Client:</span>
          <span fxFlex="60%"><a [href]="transferToClient(viewAccountTransferData.toClient)">
            {{ viewAccountTransferData.toClient.displayName }}</a>
          </span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Account Type:</span>
          <span fxFlex="60%">{{ viewAccountTransferData.toAccountType.value }}</span>
        </div>

        <div fxFlexFill>
          <span fxFlex="40%">Account No:</span>
          <span fxFlex="60%"><a [href]="transferToAccount(viewAccountTransferData.toClient, viewAccountTransferData.toAccount)">
            {{ viewAccountTransferData.toAccount.accountNo }}</a>
          </span>
        </div>

      </div>

    </mat-card-content>

    <mat-card-actions fxLayout="row" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="5px">
      <button type="button" color="primary" mat-raised-button (click)="goBack()">Back</button>
    </mat-card-actions>
  </mat-card>

</div>
