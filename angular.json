{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mifosx-web-app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "mifosx", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "outputPath": "dist/web-app", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/favicon.ico", "src/robots.txt", "src/manifest.json", "src/assets"], "styles": ["node_modules/font-awesome/css/font-awesome.css", "src/main.scss", {"inject": false, "input": "src/theme/mifosx-theme.scss", "bundleName": "mifosx-theme"}], "scripts": [], "allowedCommonJsDependencies": ["lodash", "@ckeditor/ckeditor5-build-classic"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "12mb"}, {"type": "anyComponentStyle", "maximumWarning": "64kb", "maximumError": "128kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": true, "outputHashing": "all", "extractLicenses": true, "aot": true, "buildOptimizer": true, "vendorChunk": false}, "development": {"optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": true, "aot": true, "buildOptimizer": true, "vendorChunk": false}, "default_env": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.default_env.ts"}], "optimization": true, "sourceMap": false, "namedChunks": true, "outputHashing": "all", "extractLicenses": true, "aot": true, "buildOptimizer": true, "vendorChunk": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "mifosx-web-app:build"}, "configurations": {"development": {"browserTarget": "mifosx-web-app:build:development"}, "production": {"browserTarget": "mifosx-web-app:build:production"}, "default_env": {"browserTarget": "mifosx-web-app:build:default_env"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "mifosx-web-app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "src/karma.conf.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": [], "styles": ["src/main.scss", {"inject": false, "input": "src/theme/mifosx-theme.scss", "bundleName": "mifosx-theme"}], "assets": ["src/favicon.ico", "src/robots.txt", "src/manifest.json", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json", "cypress/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "mifosx-web-app:serve"}, "configurations": {"production": {"devServerTarget": "mifosx-web-app:serve:production"}}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false}}, "e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "mifosx-web-app:serve", "watch": true, "headless": false}, "configurations": {"production": {"devServerTarget": "mifosx-web-app:serve:production"}}}}}, "mifosx-web-app-e2e": {"root": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "mifosx-web-app-e2e:serve", "watch": true, "headless": false}, "configurations": {"production": {"devServerTarget": "mifosx-web-app-e2e:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/cypress/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "mifosx-web-app-e2e:serve", "configFile": "e2e/cypress.json"}, "configurations": {"production": {"devServerTarget": "mifosx-web-app-e2e:serve:production"}}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false, "configFile": "e2e/cypress.json"}}}}}, "cli": {"analytics": false}}